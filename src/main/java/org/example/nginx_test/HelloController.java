package org.example.nginx_test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment; // 引入 Environment 类
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HelloController {

    // 注入整个 Environment 对象
    @Autowired
    private Environment environment;

    @GetMapping("/hello")
    public String sayHello() {
        // 在方法被调用时，才去获取端口号
        String serverPort = environment.getProperty("local.server.port");
        return "Hello from Spring Boot! I am running on port: " + serverPort;
    }
}